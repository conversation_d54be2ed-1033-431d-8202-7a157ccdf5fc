#!/usr/bin/env python3
"""
修复ChromaDB中的Unicode编码不一致问题
"""
import os
import sys
import json
import sqlite3
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config import settings
import chromadb

def decode_unicode_escapes(text):
    """解码Unicode转义序列"""
    if not isinstance(text, str):
        return text
    
    # 检查是否包含Unicode转义序列
    if '\\u' in text:
        try:
            # 使用正则表达式替换Unicode转义序列
            def replace_unicode(match):
                return chr(int(match.group(1), 16))
            
            # 匹配 \uXXXX 格式
            decoded = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode, text)
            return decoded
        except Exception as e:
            print(f"⚠️ Unicode解码失败: {text} -> {e}")
            return text
    
    return text

def fix_chromadb_unicode():
    """修复ChromaDB中的Unicode编码问题"""
    print("🔧 开始修复ChromaDB Unicode编码问题...")
    
    try:
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        collection = client.get_collection(settings.collection_name)
        
        # 获取所有文档
        result = collection.get(include=['metadatas'])
        
        if not result['ids']:
            print("❌ 没有找到任何文档")
            return False
        
        print(f"📊 找到 {len(result['ids'])} 个文档块")
        
        fixed_count = 0
        error_count = 0
        
        # 批量处理
        batch_size = 100
        for i in range(0, len(result['ids']), batch_size):
            batch_ids = result['ids'][i:i+batch_size]
            batch_metadatas = result['metadatas'][i:i+batch_size]
            
            updated_metadatas = []
            update_ids = []
            
            for doc_id, metadata in zip(batch_ids, batch_metadatas):
                if not metadata:
                    continue
                
                original_metadata = metadata.copy()
                updated = False
                
                # 检查并修复title字段
                if 'title' in metadata and metadata['title']:
                    original_title = metadata['title']
                    decoded_title = decode_unicode_escapes(original_title)
                    
                    if original_title != decoded_title:
                        metadata['title'] = decoded_title
                        updated = True
                        print(f"🔄 修复title: {original_title} -> {decoded_title}")
                
                # 检查其他可能包含中文的字段
                text_fields = ['filename', 'file_path', 'content']
                for field in text_fields:
                    if field in metadata and metadata[field]:
                        original_value = metadata[field]
                        decoded_value = decode_unicode_escapes(original_value)
                        
                        if original_value != decoded_value:
                            metadata[field] = decoded_value
                            updated = True
                            print(f"🔄 修复{field}: {original_value} -> {decoded_value}")
                
                if updated:
                    updated_metadatas.append(metadata)
                    update_ids.append(doc_id)
                    fixed_count += 1
            
            # 批量更新
            if update_ids:
                try:
                    collection.update(
                        ids=update_ids,
                        metadatas=updated_metadatas
                    )
                    print(f"✅ 批量更新 {len(update_ids)} 个文档")
                except Exception as e:
                    print(f"❌ 批量更新失败: {e}")
                    error_count += len(update_ids)
        
        print(f"\n📊 修复完成:")
        print(f"  - 修复的文档数: {fixed_count}")
        print(f"  - 错误数: {error_count}")
        
        return fixed_count > 0
        
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        return False

def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    
    try:
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        collection = client.get_collection(settings.collection_name)
        
        # 获取一些样本进行检查
        result = collection.get(
            limit=10,
            include=['metadatas']
        )
        
        unicode_found = False
        for i, metadata in enumerate(result['metadatas']):
            if metadata and 'title' in metadata:
                title = metadata['title']
                if '\\u' in str(title):
                    print(f"⚠️ 仍然发现Unicode转义: {title}")
                    unicode_found = True
                else:
                    print(f"✅ 正常title: {title}")
        
        if not unicode_found:
            print("🎉 所有检查的title都已正常编码")
        
        return not unicode_found
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 ChromaDB Unicode编码修复工具")
    print("="*50)
    
    # 备份提醒
    print("⚠️ 重要提醒:")
    print("  1. 请确保已备份ChromaDB数据")
    print("  2. 建议在测试环境先运行")
    print("  3. 修复过程可能需要一些时间")
    
    confirm = input("\n是否继续修复? (y/N): ")
    if confirm.lower() != 'y':
        print("❌ 用户取消操作")
        return
    
    # 执行修复
    if fix_chromadb_unicode():
        print("\n✅ 修复完成")
        
        # 验证结果
        if verify_fix():
            print("✅ 验证通过，Unicode编码问题已解决")
        else:
            print("⚠️ 验证发现仍有问题，可能需要进一步检查")
    else:
        print("❌ 修复失败")

if __name__ == "__main__":
    main()
